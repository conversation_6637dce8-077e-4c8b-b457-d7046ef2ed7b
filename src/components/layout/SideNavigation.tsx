import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import {
  Home,
  LayoutList,
  LogOut,
  Mic,
  Moon,
  Sun,
  ChevronLeft,
  ChevronRight,
  User,
  Users,
  Folder,
  FileText,
  BookOpen,
  Palette,
  Zap,
  Edit,
  MessageSquare,
} from 'lucide-react'
import { useAppDispatch, useAppSelector } from '../../store/hooks'
import { logoutUser } from '../../store/slices/authSlice'
import { useTheme } from '../../services/theme/ThemeProvider'
import { cn } from '../../utils/cn'
import { motion, AnimatePresence } from 'framer-motion'
import { useNavigation } from '../../contexts/NavigationContext'
import ProfileModal from '../ui/ProfileModal'
import AnimatedCharacter from '../ui/AnimatedCharacter'
import DropdownNavigation, { NavigationItem } from './DropdownNavigation'

/**
 * Side Navigation Component with collapsible functionality
 */
const SideNavigation: React.FC = React.memo(() => {
  const { user } = useAppSelector((state) => state.auth)
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const location = useLocation()
  const { theme, setTheme } = useTheme()
  // Preserve collapse state in localStorage, default collapsed on mobile
  const [isCollapsed, setIsCollapsed] = useState(() => {
    const saved = localStorage.getItem('sidebar-collapsed')
    const isMobile = typeof window !== 'undefined' && window.innerWidth < 640
    return saved ? JSON.parse(saved) : isMobile
  })
  const [showProfileModal, setShowProfileModal] = useState(false)

  // Save collapse state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('sidebar-collapsed', JSON.stringify(isCollapsed))
  }, [isCollapsed])

  // Close mobile menu when route changes on mobile and handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) { // md breakpoint for better mobile experience
        setIsCollapsed(true)
      }
    }

    // Close on route change for mobile
    if (window.innerWidth < 768) {
      setIsCollapsed(true)
    }

    // Add resize listener
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [location.pathname])

  // Only get breadcrumbs when needed to prevent unnecessary re-renders
  const { breadcrumbs } = useNavigation()



  const menuItems = useMemo((): NavigationItem[] => [
    { path: '/dashboard', label: 'Dashboard', icon: Home },
    { path: '/playground', label: 'Playground', icon: Mic },
    { path: '/tasks', label: 'Task History', icon: LayoutList },
    {
      path: '/editors',
      label: 'Editors Chamber',
      icon: Edit,
      children: [
        { path: '/themes', label: 'Themes', icon: Palette },
        { path: '/editors-pick', label: 'Editors Pick', icon: BookOpen },
        { path: '/editor-chamber', label: 'Create Content', icon: Zap },
        { path: '/daily-questions', label: 'Daily Questions', icon: MessageSquare },
      ]
    },
    {
      path: '/user-management',
      label: 'User Management',
      icon: Users,
      children: [
        { path: '/user-management/users', label: 'List Users', icon: Users },
      ]
    },
  ], [])



  const handleLogout = useCallback(async () => {
    await dispatch(logoutUser())
    navigate('/', { replace: true })
  }, [dispatch, navigate])

  const toggleCollapse = useCallback(() => {
    setIsCollapsed(!isCollapsed)
  }, [isCollapsed])

  const toggleTheme = useCallback(() => {
    if (theme === 'dark') {
      setTheme('light')
    } else if (theme === 'light') {
      setTheme('dark')
    } else {
      setTheme('light')
    }
  }, [theme, setTheme])

  const getThemeIcon = useMemo(() => {
    return theme === 'dark' ? <Sun className={cn(
      "transition-all duration-200",
      isCollapsed ? "h-3.5 w-3.5" : "h-4 w-4"
    )} /> : <Moon className={cn(
      "transition-all duration-200",
      isCollapsed ? "h-3.5 w-3.5" : "h-4 w-4"
    )} />
  }, [theme, isCollapsed])

  const getThemeTooltip = useMemo(() => {
    if (theme === 'dark') return 'Switch to light mode'
    if (theme === 'light') return 'Switch to dark mode'
    return 'Switch theme'
  }, [theme])

  return (
    <>
      {/* Mobile Overlay */}
      <AnimatePresence>
        {!isCollapsed && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40 md:hidden"
            onClick={() => setIsCollapsed(true)}
          />
        )}
      </AnimatePresence>

      <div className="relative flex h-screen">
        {/* Sidebar - Overlay on mobile, inline on desktop */}
        <motion.div
          className={cn(
            "flex flex-col h-screen bg-white/95 dark:bg-gray-950/95 backdrop-blur-xl border-r border-gray-200/50 dark:border-gray-800/50 transition-all duration-300",
            // Mobile: Fixed overlay positioning with better z-index
            "md:relative fixed left-0 top-0 z-50",
            // Responsive width adjustments
            isCollapsed
              ? "w-0 md:w-12 lg:w-14"
              : "w-72 sm:w-80 md:w-48 lg:w-56 xl:w-64",
            // Visibility - better mobile handling
            isCollapsed ? "md:flex hidden" : "flex",
            // Mobile slide animation
            !isCollapsed && "md:translate-x-0"
          )}
          initial={{ x: -100 }}
          animate={{ x: 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
        >
        {/* Header */}
        <motion.div
          className={cn(
            "flex items-center border-b border-gray-200/30 dark:border-gray-800/30 transition-all duration-300",
            isCollapsed ? "justify-center p-2 sm:p-3" : "justify-start p-3 lg:p-4"
          )}
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1, duration: 0.3 }}
        >
          <div className="flex items-center gap-2 min-w-0">
            <motion.div
              className={cn(
                'rounded-xl flex items-center justify-center bg-gradient-to-br',
                'from-blue-500 to-purple-600 shadow-lg transition-all duration-300',
                isCollapsed ? 'w-6 h-6 sm:w-7 sm:h-7' : 'w-7 h-7 lg:w-8 lg:h-8'
              )}
              whileHover={{ scale: 1.1, rotate: 5 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className={cn(
                "text-white font-bold",
                isCollapsed ? "text-xs" : "text-xs lg:text-sm"
              )}>NP</span>
            </motion.div>
            <AnimatePresence>
              {!isCollapsed && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                  className="min-w-0 flex-1"
                >
                  <h2 className="font-semibold text-sm lg:text-base text-sidebar-foreground truncate">
                    Nepali Learning
                  </h2>
                  <p className="text-xs lg:text-sm text-sidebar-foreground/60 truncate">
                    Learn Fluent Nepali
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>

      {/* Navigation Menu */}
      <nav className="flex-1 p-1 sm:p-1.5">
        <div className="space-y-0.5">
          {menuItems.map((item) => (
            <DropdownNavigation
              key={item.path}
              item={item}
              isCollapsed={isCollapsed}
            />
          ))}
        </div>

        {/* Dynamic Breadcrumb Navigation */}
        {!isCollapsed && breadcrumbs.length > 0 && (
          <div className="mt-4 pt-3 border-t border-sidebar-border">
            <div className="px-2.5 mb-2">
              <h3 className="text-xs font-semibold text-sidebar-foreground/60 uppercase tracking-wider">
                Current Path
              </h3>
            </div>
            <div className="space-y-0.5">
              {breadcrumbs.map((breadcrumb, index) => (
                <div key={breadcrumb.id}>
                  <Link to={breadcrumb.path}>
                    <div
                      className={cn(
                        'flex items-center gap-2 px-2.5 py-1.5 rounded-md transition-all duration-200',
                        'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground',
                        breadcrumb.isActive && 'bg-sidebar-primary/20 text-sidebar-primary',
                        !breadcrumb.isActive && 'text-sidebar-foreground/80'
                      )}
                    >
                      {breadcrumb.type === 'taskset' ? (
                        <Folder className="h-3 w-3 flex-shrink-0" />
                      ) : (
                        <FileText className="h-3 w-3 flex-shrink-0" />
                      )}
                      <span className="text-xs font-medium truncate">
                        {index > 0 && '→ '}{breadcrumb.name}
                      </span>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          </div>
        )}
      </nav>

      {/* Footer */}
      <div className="p-1.5 border-t border-sidebar-border space-y-1">
        {/* User Info */}
        {user && (
          <button
            onClick={() => setShowProfileModal(true)}
            className={cn(
              'flex items-center gap-2 px-2.5 py-1.5 rounded-md w-full text-left',
              'bg-sidebar-accent/50 hover:bg-sidebar-accent transition-colors',
              isCollapsed && 'justify-center px-1.5'
            )}
            title={isCollapsed ? `${user.full_name || user.username} - Click for profile` : 'View profile'}
          >
            {user.profile_picture ? (
              <img
                src={user.profile_picture}
                alt={user.full_name || user.username}
                className="w-7 h-7 rounded-full border border-sidebar-border"
              />
            ) : (
              <div className="w-7 h-7">
                <AnimatedCharacter
                  size="sm"
                  character="wizard"
                  mood="happy"
                  className="scale-75"
                />
              </div>
            )}
            <AnimatePresence>
              {!isCollapsed && (
                <motion.div
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  className="flex-1 min-w-0"
                >
                  <p className="font-medium text-xs text-sidebar-foreground truncate">
                    {user.full_name || user.username}
                  </p>
                  <p className="text-xs text-sidebar-foreground/60 truncate">
                    {user.email}
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </button>
        )}

        {/* Theme Toggle */}
        <div className={cn(
          'flex items-center',
          isCollapsed ? 'justify-center' : 'px-2.5'
        )}>
          <button
            onClick={toggleTheme}
            className={cn(
              'flex items-center justify-center p-1.5 rounded-md transition-colors w-full',
              'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground text-sidebar-foreground',
              !isCollapsed && 'gap-2'
            )}
            title={getThemeTooltip}
          >
            <AnimatePresence mode="wait">
              <motion.div
                key={theme}
                initial={{ opacity: 0, rotate: -180 }}
                animate={{ opacity: 1, rotate: 0 }}
                exit={{ opacity: 0, rotate: 180 }}
                transition={{ duration: 0.3 }}
              >
                {getThemeIcon}
              </motion.div>
            </AnimatePresence>
            <AnimatePresence>
              {!isCollapsed && (
                <motion.span
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  className="text-xs font-medium flex-1 text-left"
                >
                  {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}
                </motion.span>
              )}
            </AnimatePresence>
          </button>
        </div>

        {/* Logout Button */}
        <div className={cn(
          'flex items-center',
          isCollapsed ? 'justify-center' : 'px-2.5'
        )}>
          <button
            onClick={handleLogout}
            className={cn(
              'flex items-center justify-center p-1.5 rounded-md transition-colors w-full',
              'hover:bg-red-500/10 text-red-500 hover:text-red-600',
              !isCollapsed && 'gap-2'
            )}
            title="Logout"
          >
            <LogOut className="h-4 w-4" />
            <AnimatePresence>
              {!isCollapsed && (
                <motion.span
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  className="text-xs font-medium flex-1 text-left"
                >
                  Logout
                </motion.span>
              )}
            </AnimatePresence>
          </button>
        </div>
      </div>
      </motion.div>

        {/* External Collapse/Expand Button - Visible on all devices */}
        <button
          onClick={toggleCollapse}
          className={cn(
            'absolute top-3 z-10 p-2 rounded-full transition-all duration-300',
            'bg-background border border-border shadow-md hover:shadow-lg',
            'hover:bg-accent text-foreground hover:scale-110',
            isCollapsed ? '-right-6' : '-right-6'
          )}
          title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {isCollapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </button>
      </div>

      {/* Profile Modal */}
      <ProfileModal
        open={showProfileModal}
        onOpenChange={setShowProfileModal}
      />
    </>
  )
})

SideNavigation.displayName = 'SideNavigation'

export default SideNavigation
