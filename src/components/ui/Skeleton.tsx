import React from 'react'
import { cn } from '../../utils/cn'

interface SkeletonProps {
  className?: string
}

/**
 * Basic Skeleton component for loading states
 */
const Skeleton: React.FC<SkeletonProps> = ({ className }) => {
  return (
    <div
      className={cn(
        "animate-pulse rounded-md bg-gray-200 dark:bg-gray-700",
        className
      )}
    />
  )
}

/**
 * Skeleton for TaskSet Detail Header
 */
export const TaskSetDetailSkeleton: React.FC = () => {
  return (
    <div className="bg-card border border-border rounded-xl p-6 shadow-sm">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <Skeleton className="w-12 h-12 rounded-lg" />
          <div className="flex-1">
            <div className="flex items-center gap-4 text-sm">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-1" />
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-1" />
              <Skeleton className="h-4 w-20" />
            </div>
          </div>
        </div>
        <Skeleton className="w-8 h-8 rounded-lg" />
      </div>

      {/* Audio Player Skeleton */}
      <div className="mb-6">
        <div className="bg-card border border-border rounded-xl p-6 shadow-lg">
          <Skeleton className="h-6 w-48 mb-4" />
          <Skeleton className="h-20 w-full rounded-lg mb-4" />
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-4 w-12" />
            </div>
            <div className="flex items-center justify-center space-x-4">
              <Skeleton className="w-10 h-10 rounded-full" />
              <Skeleton className="w-12 h-12 rounded-full" />
              <Skeleton className="w-10 h-10 rounded-full" />
            </div>
            <div className="flex items-center space-x-3">
              <Skeleton className="w-6 h-6" />
              <Skeleton className="h-2 flex-1 rounded-lg" />
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <Skeleton className="w-full h-2 rounded-full mb-4" />

      {/* Stats */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="text-center">
          <Skeleton className="h-8 w-8 mx-auto mb-2" />
          <Skeleton className="h-4 w-16 mx-auto" />
        </div>
        <div className="text-center">
          <Skeleton className="h-8 w-8 mx-auto mb-2" />
          <Skeleton className="h-4 w-16 mx-auto" />
        </div>
        <div className="text-center">
          <Skeleton className="h-8 w-8 mx-auto mb-2" />
          <Skeleton className="h-4 w-16 mx-auto" />
        </div>
      </div>

      {/* View Output Button */}
      <div className="flex justify-center">
        <Skeleton className="h-12 w-32 rounded-lg" />
      </div>
    </div>
  )
}

/**
 * Skeleton for Task Items Grid
 */
export const TaskItemsSkeleton: React.FC<{ count?: number }> = ({ count = 6 }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="bg-card border border-border rounded-xl p-4 shadow-sm">
          {/* Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-2">
              <Skeleton className="w-6 h-6 rounded-full" />
              <Skeleton className="w-6 h-6 rounded-full" />
            </div>
            <Skeleton className="w-4 h-4" />
          </div>

          {/* Content */}
          <Skeleton className="h-5 w-full mb-2" />
          <Skeleton className="h-5 w-3/4 mb-4" />

          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-12" />
          </div>
        </div>
      ))}
    </div>
  )
}

/**
 * Skeleton for Audio Player
 */
export const AudioPlayerSkeleton: React.FC = () => {
  return (
    <div className="bg-card border border-border rounded-xl p-6 shadow-lg">
      <Skeleton className="h-6 w-48 mb-4" />
      <Skeleton className="h-20 w-full rounded-lg mb-4" />
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-4 w-12" />
          <Skeleton className="h-4 w-12" />
        </div>
        <div className="flex items-center justify-center space-x-4">
          <Skeleton className="w-10 h-10 rounded-full" />
          <Skeleton className="w-12 h-12 rounded-full" />
          <Skeleton className="w-10 h-10 rounded-full" />
        </div>
        <div className="flex items-center space-x-3">
          <Skeleton className="w-6 h-6" />
          <Skeleton className="h-2 flex-1 rounded-lg" />
        </div>
      </div>
    </div>
  )
}

/**
 * Skeleton for TaskItem Detail Page
 */
export const TaskItemSkeleton: React.FC = () => {
  return (
    <div className="h-[calc(100vh-4rem)] flex flex-col max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-3 border-b border-border">
        <div className="flex items-center gap-2">
          <Skeleton className="w-4 h-4" />
          <Skeleton className="w-16 h-4" />
        </div>

        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <Skeleton className="w-20 h-4" />
            <Skeleton className="w-1 h-4" />
            <Skeleton className="w-16 h-4" />
            <Skeleton className="w-1 h-4" />
            <Skeleton className="w-20 h-4" />
          </div>
          <div className="flex items-center gap-1">
            <Skeleton className="w-8 h-8 rounded-lg" />
            <Skeleton className="w-8 h-8 rounded-lg" />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 space-y-4 overflow-y-auto">
        {/* Question */}
        <div className="text-center">
          <Skeleton className="h-8 w-96 mx-auto mb-4" />
        </div>

        {/* Media */}
        <div className="flex justify-center">
          <Skeleton className="w-80 h-60 rounded-lg" />
        </div>

        {/* Options */}
        <div className="max-w-2xl mx-auto">
          <div className="grid grid-cols-2 gap-2">
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={index} className="h-12 rounded-lg" />
            ))}
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Skeleton className="h-12 w-32 rounded-lg" />
        </div>
      </div>
    </div>
  )
}

/**
 * Skeleton for Question Edit Form
 */
export const QuestionEditFormSkeleton: React.FC = () => {
  return (
    <div className="bg-white dark:bg-slate-900 rounded-2xl p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Skeleton className="h-6 w-64 mb-2" />
          <Skeleton className="h-4 w-32" />
        </div>
      </div>

      {/* Media Section */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-xl p-6 border border-blue-200/50 dark:border-blue-800/30">
        <div className="flex items-center gap-2 mb-4">
          <Skeleton className="w-5 h-5 rounded" />
          <Skeleton className="h-5 w-32" />
        </div>

        {/* Audio Player Skeleton */}
        <div className="flex items-center gap-3 p-3 bg-slate-100 dark:bg-slate-800 rounded-lg">
          <Skeleton className="w-10 h-10 rounded-full" />
          <div className="flex-1">
            <Skeleton className="h-2 w-full rounded-full mb-2" />
            <div className="flex justify-between">
              <Skeleton className="h-3 w-12" />
              <Skeleton className="h-3 w-12" />
            </div>
          </div>
          <Skeleton className="w-4 h-4" />
        </div>
      </div>

      {/* Form Fields */}
      <div className="space-y-4">
        {/* Question Text */}
        <div>
          <Skeleton className="h-4 w-24 mb-2" />
          <Skeleton className="h-20 w-full rounded-lg" />
        </div>

        {/* Translated Text */}
        <div>
          <Skeleton className="h-4 w-32 mb-2" />
          <Skeleton className="h-20 w-full rounded-lg" />
        </div>

        {/* Options */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-8 w-24 rounded-lg" />
          </div>
          <div className="space-y-4">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="flex items-center gap-3 p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
                <Skeleton className="w-5 h-5 rounded" />
                <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Skeleton className="h-10 w-full rounded" />
                  <Skeleton className="h-10 w-full rounded" />
                </div>
                <Skeleton className="w-8 h-8 rounded" />
              </div>
            ))}
          </div>
        </div>

        {/* Answer Hint */}
        <div>
          <Skeleton className="h-4 w-28 mb-2" />
          <Skeleton className="h-16 w-full rounded-lg" />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 pt-4 border-t border-slate-200 dark:border-slate-700">
        <Skeleton className="h-10 w-20 rounded-lg" />
        <Skeleton className="h-10 w-32 rounded-lg" />
      </div>
    </div>
  )
}

export default Skeleton
