import React, { useState, useRef, useCallback, memo } from 'react'
import { Play, Pause, Volume2 } from 'lucide-react'
import { cn } from '../../utils/cn'

interface OptimizedAudioPlayerProps {
  src: string
  title?: string
  className?: string
}

/**
 * Optimized Audio Player Component
 * - Uses memo to prevent unnecessary re-renders
 * - Manages its own internal state
 * - Doesn't trigger parent component updates
 * - Uses useCallback for event handlers to maintain referential equality
 */
const OptimizedAudioPlayer: React.FC<OptimizedAudioPlayerProps> = memo(({ 
  src, 
  title, 
  className 
}) => {
  const audioRef = useRef<HTMLAudioElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  // Memoized event handlers to prevent re-renders
  const togglePlay = useCallback(() => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
    } else {
      setIsLoading(true)
      audioRef.current.play().catch((error) => {
        console.error('Audio play failed:', error)
        setIsLoading(false)
      })
    }
  }, [isPlaying])

  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime)
    }
  }, [])

  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration)
    }
  }, [])

  const handlePlay = useCallback(() => {
    setIsPlaying(true)
    setIsLoading(false)
  }, [])

  const handlePause = useCallback(() => {
    setIsPlaying(false)
    setIsLoading(false)
  }, [])

  const handleEnded = useCallback(() => {
    setIsPlaying(false)
    setIsLoading(false)
  }, [])

  const handleSeek = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!audioRef.current || !duration) return

    const rect = e.currentTarget.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const percentage = clickX / rect.width
    const newTime = percentage * duration

    audioRef.current.currentTime = newTime
    setCurrentTime(newTime)
  }, [duration])

  const formatTime = useCallback((time: number) => {
    if (isNaN(time)) return '0:00'
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }, [])

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0

  return (
    <div className={cn(
      'flex items-center gap-3 p-3 bg-slate-100 dark:bg-slate-800 rounded-lg',
      className
    )}>
      <audio
        ref={audioRef}
        src={src}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onPlay={handlePlay}
        onPause={handlePause}
        onEnded={handleEnded}
        preload="metadata"
      />

      {/* Play/Pause Button */}
      <button
        onClick={togglePlay}
        disabled={isLoading}
        className={cn(
          'p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-full transition-colors',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          'flex items-center justify-center min-w-[40px] min-h-[40px]'
        )}
      >
        {isLoading ? (
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
        ) : isPlaying ? (
          <Pause className="w-4 h-4" />
        ) : (
          <Play className="w-4 h-4" />
        )}
      </button>

      {/* Progress Bar and Time */}
      <div className="flex-1 min-w-0">
        {/* Progress Bar */}
        <div 
          className="w-full h-2 bg-slate-300 dark:bg-slate-600 rounded-full cursor-pointer mb-1"
          onClick={handleSeek}
        >
          <div 
            className="h-full bg-blue-500 rounded-full transition-all duration-100"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>

        {/* Time Display */}
        <div className="flex justify-between text-xs text-slate-600 dark:text-slate-400">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>

      {/* Audio Icon */}
      <Volume2 className="w-4 h-4 text-blue-500 flex-shrink-0" />

      {/* Title (if provided) */}
      {title && (
        <div className="text-sm text-slate-700 dark:text-slate-300 truncate max-w-[120px]">
          {title}
        </div>
      )}
    </div>
  )
})

OptimizedAudioPlayer.displayName = 'OptimizedAudioPlayer'

export default OptimizedAudioPlayer
