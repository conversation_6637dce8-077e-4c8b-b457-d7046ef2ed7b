import { useState, useCallback, useRef, useMemo } from 'react'

interface QuestionData {
  id: string
  type: string
  title: string
  question: {
    text: string
    translated_text: string
    options: Record<string, string>
    options_en: Record<string, string>
    answer_hint?: string
    correct_answer_index?: number
    metadata?: {
      url?: string
      content_type?: string
      keyword?: string
    }
    image_metadata?: {
      url?: string
    }
    options_metadata?: Record<string, { audio_url?: string }>
  }
  correct_answer?: {
    type: string
    value?: string | string[]
  }
}

interface OptimisticUpdate {
  id: string
  timestamp: number
  data: Partial<QuestionData>
}

interface UseOptimisticQuestionStateOptions {
  initialData: QuestionData | null
  onSave: (taskId: string, updatedData: any) => Promise<void>
  onSaveSuccess?: () => void
}

interface UseOptimisticQuestionStateReturn {
  questionData: QuestionData | null
  isLoading: boolean
  isSaving: boolean
  error: string | null
  updateQuestion: (updates: Partial<QuestionData>) => void
  saveQuestion: (formData: any) => Promise<void>
  refreshData: (newData: QuestionData) => void
  hasUnsavedChanges: boolean
}

/**
 * Custom hook for managing question state with optimistic updates
 * Prevents unnecessary refetching and provides smooth user experience
 */
export const useOptimisticQuestionState = ({
  initialData,
  onSave,
  onSaveSuccess
}: UseOptimisticQuestionStateOptions): UseOptimisticQuestionStateReturn => {
  const [baseData, setBaseData] = useState<QuestionData | null>(initialData)
  const [optimisticUpdates, setOptimisticUpdates] = useState<OptimisticUpdate[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const updateCounterRef = useRef(0)
  const lastSavedDataRef = useRef<QuestionData | null>(initialData)

  // Compute the current question data by applying optimistic updates
  const questionData = useMemo(() => {
    if (!baseData) return null

    // Apply optimistic updates in chronological order
    return optimisticUpdates.reduce((current, update) => {
      return {
        ...current,
        ...update.data,
        question: {
          ...current.question,
          ...(update.data.question || {})
        }
      }
    }, baseData)
  }, [baseData, optimisticUpdates])

  // Check if there are unsaved changes
  const hasUnsavedChanges = useMemo(() => {
    return optimisticUpdates.length > 0
  }, [optimisticUpdates])

  // Update question data optimistically (for UI interactions)
  const updateQuestion = useCallback((updates: Partial<QuestionData>) => {
    const updateId = `update_${++updateCounterRef.current}`
    const timestamp = Date.now()

    setOptimisticUpdates(prev => [
      ...prev,
      {
        id: updateId,
        timestamp,
        data: updates
      }
    ])

    // Clear error when user makes changes
    if (error) {
      setError(null)
    }
  }, [error])

  // Save question data to server
  const saveQuestion = useCallback(async (formData: any) => {
    if (!questionData) return

    try {
      setIsSaving(true)
      setError(null)

      // Prepare update data
      const updateData = {
        text: formData.text,
        translated_text: formData.translated_text,
        options: formData.options,
        options_en: formData.options_en,
        answer_hint: formData.answer_hint,
        correct_answer: formData.correct_answer,
        regenerate_audio: true
      }

      // Call the save function
      await onSave(questionData.id, updateData)

      // Clear optimistic updates after successful save
      setOptimisticUpdates([])
      
      // Update last saved data reference
      lastSavedDataRef.current = questionData

      // Call success callback
      onSaveSuccess?.()

    } catch (err) {
      console.error('Error saving question:', err)
      setError(err instanceof Error ? err.message : 'Failed to save question')
      throw err
    } finally {
      setIsSaving(false)
    }
  }, [questionData, onSave, onSaveSuccess])

  // Refresh data from external source (only when needed)
  const refreshData = useCallback((newData: QuestionData) => {
    setBaseData(newData)
    
    // Only clear optimistic updates if the new data is significantly different
    // This prevents clearing user changes during normal updates
    const hasSignificantChanges = lastSavedDataRef.current && (
      lastSavedDataRef.current.id !== newData.id ||
      lastSavedDataRef.current.question.text !== newData.question.text ||
      JSON.stringify(lastSavedDataRef.current.question.options) !== JSON.stringify(newData.question.options)
    )

    if (hasSignificantChanges) {
      setOptimisticUpdates([])
    }

    lastSavedDataRef.current = newData
    setError(null)
  }, [])

  // Clean up old optimistic updates (older than 5 minutes)
  const cleanupOldUpdates = useCallback(() => {
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000
    setOptimisticUpdates(prev => 
      prev.filter(update => update.timestamp > fiveMinutesAgo)
    )
  }, [])

  // Periodically clean up old updates
  useState(() => {
    const interval = setInterval(cleanupOldUpdates, 60000) // Every minute
    return () => clearInterval(interval)
  })

  return {
    questionData,
    isLoading,
    isSaving,
    error,
    updateQuestion,
    saveQuestion,
    refreshData,
    hasUnsavedChanges
  }
}
