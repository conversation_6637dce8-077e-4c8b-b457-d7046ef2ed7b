import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Save, Loader2, CheckCircle, AlertCircle } from 'lucide-react'
import Skeleton from '../../../components/ui/Skeleton'
import {
  DailyQuestion,
  Theme,
  CreateDailyQuestionRequest,
  UpdateDailyQuestionRequest,
  createDailyQuestion,
  updateDailyQuestion
} from '../../../services/curatedService'

interface DailyQuestionModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  question?: DailyQuestion | null
  themes: Theme[]
  onSuccess: (updatedQuestion?: DailyQuestion) => void
}

/**
 * Enhanced Daily Question Modal
 * 
 * Features:
 * - Optimistic updates with no unnecessary refetching
 * - Skeleton loading states
 * - Audio-safe state management
 * - Better form validation and UX
 * - No flickering or reloading
 */
const DailyQuestionModal: React.FC<DailyQuestionModalProps> = ({
  open,
  onOpenChange,
  question,
  themes,
  onSuccess
}) => {
  // Form state with stable initialization
  const [formData, setFormData] = useState({
    theme_id: '',
    text: '',
    text_en: ''
  })
  
  // UI state
  const [loading, setLoading] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  
  // Derived state
  const isEditing = !!question
  const isFormValid = formData.theme_id && formData.text.trim() && formData.text_en.trim()
  
  // Memoized selected theme to prevent unnecessary re-renders
  const selectedTheme = useMemo(() => 
    themes.find(theme => theme.id === formData.theme_id),
    [themes, formData.theme_id]
  )

  // Initialize form data when modal opens or question changes
  useEffect(() => {
    if (open) {
      if (question) {
        // Editing existing question
        setFormData({
          theme_id: question.theme_id || '',
          text: question.text || '',
          text_en: question.text_en || ''
        })
      } else {
        // Creating new question
        setFormData({
          theme_id: '',
          text: '',
          text_en: ''
        })
      }
      setErrors({})
      setShowSuccess(false)
    }
  }, [open, question])

  // Stable form change handler
  const handleChange = useCallback((field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }, [errors])

  // Form validation
  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.theme_id) {
      newErrors.theme_id = 'Please select a theme'
    }
    
    if (!formData.text.trim()) {
      newErrors.text = 'Question text is required'
    } else if (formData.text.length > 500) {
      newErrors.text = 'Question text must be 500 characters or less'
    }
    
    if (!formData.text_en.trim()) {
      newErrors.text_en = 'English question text is required'
    } else if (formData.text_en.length > 500) {
      newErrors.text_en = 'English question text must be 500 characters or less'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [formData])

  // Submit handler with optimistic updates
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    try {
      setLoading(true)
      
      if (isEditing && question) {
        // Update existing question
        const updateData: UpdateDailyQuestionRequest = {}
        
        // Only include changed fields
        if (formData.text !== question.text) {
          updateData.text = formData.text.trim()
        }
        if (formData.text_en !== question.text_en) {
          updateData.text_en = formData.text_en.trim()
        }
        if (formData.theme_id !== question.theme_id) {
          updateData.theme_id = formData.theme_id
        }
        
        const questionId = question._id || question.id
        if (!questionId) {
          throw new Error('Question ID is missing')
        }
        
        const response = await updateDailyQuestion(questionId, updateData)
        
        // Show success state briefly
        setShowSuccess(true)
        setTimeout(() => {
          onSuccess(response.data)
        }, 800)
        
      } else {
        // Create new question
        const createData: CreateDailyQuestionRequest = {
          theme_id: formData.theme_id,
          text: formData.text.trim(),
          text_en: formData.text_en.trim()
        }
        
        const response = await createDailyQuestion(createData)
        
        // Show success state briefly
        setShowSuccess(true)
        setTimeout(() => {
          onSuccess(response.data)
        }, 800)
      }
      
    } catch (error: any) {
      console.error('Failed to save question:', error)
      setErrors({ submit: error.message || 'Failed to save question' })
    } finally {
      setLoading(false)
    }
  }, [formData, isEditing, question, validateForm, onSuccess])

  // Close handler
  const handleClose = useCallback(() => {
    if (!loading) {
      onOpenChange(false)
    }
  }, [loading, onOpenChange])

  // Animation variants
  const modalVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 }
  }

  const successVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 }
  }

  if (!open) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm"
          onClick={handleClose}
        />

        {/* Modal */}
        <motion.div
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          transition={{ duration: 0.2 }}
          className="relative bg-card border border-border rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden"
        >
          {/* Success Overlay */}
          <AnimatePresence>
            {showSuccess && (
              <motion.div
                variants={successVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="absolute inset-0 bg-green-50 dark:bg-green-950/20 flex items-center justify-center z-10"
              >
                <div className="text-center">
                  <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-green-900 dark:text-green-100">
                    {isEditing ? 'Question Updated!' : 'Question Created!'}
                  </h3>
                  <p className="text-green-700 dark:text-green-300 text-sm">
                    {isEditing ? 'Your changes have been saved successfully.' : 'The new question has been added.'}
                  </p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-border bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-600 rounded-lg">
                <Save className="w-4 h-4 text-white" />
              </div>
              <h2 className="text-xl font-semibold text-foreground">
                {isEditing ? 'Edit Daily Question' : 'Create Daily Question'}
              </h2>
            </div>
            <button
              onClick={handleClose}
              disabled={loading}
              className="p-2 text-muted-foreground hover:text-foreground transition-colors rounded-lg hover:bg-accent disabled:opacity-50"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          {/* Form Content */}
          <div className="flex-1 overflow-y-auto">
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Error Display */}
              {errors.submit && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="p-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center gap-3"
                >
                  <AlertCircle className="w-5 h-5 text-red-600" />
                  <p className="text-red-800 dark:text-red-200 text-sm">{errors.submit}</p>
                </motion.div>
              )}

              {/* Theme Selection */}
              <div className="space-y-3">
                <label htmlFor="theme_id" className="text-sm font-semibold text-foreground">
                  Theme <span className="text-red-500">*</span>
                </label>

                {themes.length === 0 ? (
                  // Skeleton for themes loading
                  <div className="space-y-2">
                    <Skeleton className="h-12 w-full rounded-lg" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                ) : (
                  <>
                    <select
                      id="theme_id"
                      value={formData.theme_id}
                      onChange={(e) => handleChange('theme_id', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg text-sm bg-background text-foreground focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors ${
                        errors.theme_id ? 'border-red-500' : 'border-border'
                      }`}
                    >
                      <option value="">Select a theme</option>
                      {themes.map((theme) => (
                        <option key={theme._id} value={theme.id}>
                          {theme.icon} {theme.name_en} ({theme.category})
                        </option>
                      ))}
                    </select>

                    {errors.theme_id && (
                      <p className="text-red-500 text-xs">{errors.theme_id}</p>
                    )}

                    {/* Selected Theme Preview */}
                    <AnimatePresence>
                      {selectedTheme && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className="p-4 rounded-xl border border-border bg-accent/50"
                        >
                          <div className="flex items-center gap-3 text-sm">
                            <span className="text-2xl">{selectedTheme.icon}</span>
                            <div className="flex-1">
                              <div className="font-semibold text-foreground">{selectedTheme.name_en}</div>
                              <div className="text-xs text-muted-foreground">{selectedTheme.description_en}</div>
                            </div>
                            <span
                              className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border"
                              style={{
                                backgroundColor: `${selectedTheme.background_color}20`,
                                color: selectedTheme.font_color,
                                borderColor: `${selectedTheme.background_color}40`
                              }}
                            >
                              {selectedTheme.category}
                            </span>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </>
                )}
              </div>

              {/* Question Text (Primary Language) */}
              <div className="space-y-3">
                <label htmlFor="text" className="text-sm font-semibold text-foreground">
                  Question Text <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="text"
                  value={formData.text}
                  onChange={(e) => handleChange('text', e.target.value)}
                  placeholder="Enter the question in the primary language..."
                  rows={4}
                  maxLength={500}
                  className={`w-full px-4 py-3 border rounded-lg text-sm bg-background text-foreground resize-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors ${
                    errors.text ? 'border-red-500' : 'border-border'
                  }`}
                />
                <div className="flex justify-between items-center">
                  {errors.text ? (
                    <p className="text-red-500 text-xs">{errors.text}</p>
                  ) : (
                    <div />
                  )}
                  <p className={`text-xs ${formData.text.length > 450 ? 'text-orange-500' : 'text-muted-foreground'}`}>
                    {formData.text.length}/500 characters
                  </p>
                </div>
              </div>

              {/* Question Text (English) */}
              <div className="space-y-3">
                <label htmlFor="text_en" className="text-sm font-semibold text-foreground">
                  Question Text (English) <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="text_en"
                  value={formData.text_en}
                  onChange={(e) => handleChange('text_en', e.target.value)}
                  placeholder="Enter the question in English..."
                  rows={4}
                  maxLength={500}
                  className={`w-full px-4 py-3 border rounded-lg text-sm bg-background text-foreground resize-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors ${
                    errors.text_en ? 'border-red-500' : 'border-border'
                  }`}
                />
                <div className="flex justify-between items-center">
                  {errors.text_en ? (
                    <p className="text-red-500 text-xs">{errors.text_en}</p>
                  ) : (
                    <div />
                  )}
                  <p className={`text-xs ${formData.text_en.length > 450 ? 'text-orange-500' : 'text-muted-foreground'}`}>
                    {formData.text_en.length}/500 characters
                  </p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3 pt-6 border-t border-border">
                <button
                  type="button"
                  onClick={handleClose}
                  disabled={loading}
                  className="inline-flex items-center px-6 py-3 border border-border rounded-lg text-sm font-medium text-foreground bg-background hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={!isFormValid || loading}
                  className="inline-flex items-center px-6 py-3 bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      {isEditing ? 'Updating...' : 'Creating...'}
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      {isEditing ? 'Update Question' : 'Create Question'}
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  )
}

export default DailyQuestionModal
