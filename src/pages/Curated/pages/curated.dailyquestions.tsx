import React, { useState, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Plus, Filter, RefreshCw, Edit, Trash2, MessageSquare, Clock } from 'lucide-react'
import MainLayout from '../../../components/layout/MainLayout'
import Skeleton from '../../../components/ui/Skeleton'
import { cn } from '../../../utils/cn'
import { formatTimeAgo } from '../../../utils/dateTimeHelper'
import {
  DailyQuestion,
  DailyQuestionsQuery,
  Theme,
  getDailyQuestions,
  getThemes,
  deleteDailyQuestion
} from '../../../services/curatedService'
import DailyQuestionModal from '../components/DailyQuestionModal'
import ConfirmDialog from '../../../components/ui/ConfirmDialog'

/**
 * Daily Questions Page
 * Management interface for daily questions with theme filtering and CRUD operations
 */
const DailyQuestions: React.FC = () => {
  // State management
  const [questions, setQuestions] = useState<DailyQuestion[]>([])
  const [themes, setThemes] = useState<Theme[]>([])
  const [loading, setLoading] = useState(true)
  const [themesLoading, setThemesLoading] = useState(true)
  const [totalItems, setTotalItems] = useState(0)
  const [totalPages, setTotalPages] = useState(0)
  
  // Filter and pagination state
  const [filter, setFilter] = useState<DailyQuestionsQuery>({
    page: 1,
    limit: 10,
    sort_by: 'created_at',
    sort_order: 'desc'
  })
  
  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingQuestion, setEditingQuestion] = useState<DailyQuestion | null>(null)
  const [modalLoading, setModalLoading] = useState(false)

  // Confirmation dialog state
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [questionToDelete, setQuestionToDelete] = useState<string | null>(null)

  // Load questions
  const loadQuestions = useCallback(async () => {
    try {
      setLoading(true)
      const response = await getDailyQuestions(filter)
      setQuestions(response.data)
      setTotalItems(response.meta.total)
      setTotalPages(response.meta.total_pages)
    } catch (error) {
      console.error('Failed to load daily questions:', error)
      alert('Failed to load daily questions')
    } finally {
      setLoading(false)
    }
  }, [filter])

  // Load themes for filter dropdown
  const loadThemes = useCallback(async () => {
    try {
      setThemesLoading(true)
      const response = await getThemes({ limit: 100, is_active: true })
      setThemes(response.data)
    } catch (error) {
      console.error('Failed to load themes:', error)
      setThemes([]) // Set empty array on error
    } finally {
      setThemesLoading(false)
    }
  }, [])

  // Load data on mount and filter changes
  useEffect(() => {
    loadQuestions()
  }, [loadQuestions])

  useEffect(() => {
    loadThemes()
  }, [loadThemes])

  // Handle filter changes
  const handleFilterChange = (newFilter: Partial<DailyQuestionsQuery>) => {
    setFilter(prev => ({ ...prev, ...newFilter, page: 1 }))
  }

  // Handle page change
  const handlePageChange = (page: number) => {
    setFilter(prev => ({ ...prev, page }))
  }

  // Handle create question
  const handleCreateQuestion = () => {
    setEditingQuestion(null)
    setIsModalOpen(true)
  }

  // Handle edit question
  const handleEditQuestion = (question: DailyQuestion) => {
    console.log('Edit question clicked:', question)
    console.log('Question _id:', question._id)
    console.log('Question id:', question.id)
    setEditingQuestion(question)
    setIsModalOpen(true)
  }

  // Handle delete question - show confirmation dialog
  const handleDeleteQuestion = (questionId: string) => {
    setQuestionToDelete(questionId)
    setShowDeleteConfirm(true)
  }

  // Confirm delete question
  const confirmDeleteQuestion = async () => {
    if (!questionToDelete) return

    try {
      await deleteDailyQuestion(questionToDelete)
      console.log('Question deleted successfully')
      loadQuestions()
    } catch (error) {
      console.error('Failed to delete question:', error)
      alert('Failed to delete question')
    } finally {
      setQuestionToDelete(null)
    }
  }

  // Handle modal success
  const handleModalSuccess = () => {
    setIsModalOpen(false)
    setEditingQuestion(null)
    loadQuestions()
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  }

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <MainLayout
      title="📝 Daily Questions"
      description="Manage daily questions for themes"
    >
      <div className="h-[calc(100vh-8rem)] flex flex-col overflow-x-hidden">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="flex flex-col h-full min-w-0"
        >
          {/* Fixed Filter Panel */}
          <div className="flex-shrink-0 p-6 pb-0">
            {/* Modern Filter Panel */}
          <motion.div
            variants={cardVariants}
            className="bg-gradient-to-r from-blue-50/50 via-indigo-50/50 to-purple-50/50 dark:from-blue-950/20 dark:via-indigo-950/20 dark:to-purple-950/20 rounded-xl border border-blue-200/50 dark:border-blue-800/30 p-6 backdrop-blur-sm"
          >
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                  <Filter className="w-4 h-4 text-white" />
                </div>
                <h3 className="text-lg font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  Filter Questions
                </h3>
                <MessageSquare className="w-4 h-4 text-blue-500" />
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-2">
                <button
                  onClick={loadQuestions}
                  disabled={loading}
                  className="p-2 rounded-lg border border-blue-200/50 dark:border-blue-800/30 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 hover:border-blue-300/70 dark:hover:border-blue-700/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <RefreshCw className={cn("w-4 h-4", loading && "animate-spin")} />
                </button>
                <button
                  onClick={handleCreateQuestion}
                  className="flex items-center gap-2 px-3 py-2 rounded-lg bg-green-500 text-white hover:bg-green-600 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500/20"
                >
                  <Plus className="w-4 h-4" />
                  <span className="text-sm font-medium">Add Question</span>
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Theme Filter */}
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  Theme
                </label>
                <select
                  value={filter.theme_id || 'all'}
                  onChange={(e) => {
                    const selectedValue = e.target.value === 'all' ? undefined : e.target.value
                    console.log('Theme filter changed:', selectedValue)
                    handleFilterChange({ theme_id: selectedValue })
                  }}
                  className="w-full px-4 py-3 rounded-lg border border-blue-200/50 dark:border-blue-800/30 bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500/50 hover:border-blue-300/70 dark:hover:border-blue-700/50 transition-all duration-200 appearance-none cursor-pointer"
                >
                  <option value="all">All themes</option>
                  {themes.map((theme) => (
                    <option key={theme._id} value={theme.id}>
                      {theme.icon} {theme.name_en}
                    </option>
                  ))}
                </select>
              </div>

              {/* Sort By */}
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  Sort By
                </label>
                <select
                  value={filter.sort_by || 'created_at'}
                  onChange={(e) => handleFilterChange({ sort_by: e.target.value })}
                  className="w-full px-4 py-3 rounded-lg border border-green-200/50 dark:border-green-800/30 bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-green-500/30 focus:border-green-500/50 hover:border-green-300/70 dark:hover:border-green-700/50 transition-all duration-200 appearance-none cursor-pointer"
                >
                  <option value="created_at">Created Date</option>
                  <option value="text">Question Text</option>
                  <option value="theme_id">Theme</option>
                </select>
              </div>

              {/* Sort Order */}
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  Order
                </label>
                <select
                  value={filter.sort_order || 'desc'}
                  onChange={(e) => handleFilterChange({ sort_order: e.target.value as 'asc' | 'desc' })}
                  className="w-full px-4 py-3 rounded-lg border border-purple-200/50 dark:border-purple-800/30 bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-purple-500/30 focus:border-purple-500/50 hover:border-purple-300/70 dark:hover:border-purple-700/50 transition-all duration-200 appearance-none cursor-pointer"
                >
                  <option value="desc">Newest First</option>
                  <option value="asc">Oldest First</option>
                </select>
              </div>

              {/* Page Size */}
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  Per Page
                </label>
                <select
                  value={filter.limit?.toString() || '10'}
                  onChange={(e) => handleFilterChange({ limit: parseInt(e.target.value) })}
                  className="w-full px-4 py-3 rounded-lg border border-orange-200/50 dark:border-orange-800/30 bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-orange-500/30 focus:border-orange-500/50 hover:border-orange-300/70 dark:hover:border-orange-700/50 transition-all duration-200 appearance-none cursor-pointer"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
              </div>
            </div>
          </motion.div>
          </div>

          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto overflow-x-hidden px-6 pb-2">
            <motion.div variants={cardVariants}>
            {loading ? (
              // Loading skeletons
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 min-w-0 py-2">
                {Array.from({ length: 12 }).map((_, index) => (
                  <div key={index} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="p-4">
                      <div className="space-y-3">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                        <div className="flex gap-2">
                          <Skeleton className="h-6 w-16" />
                          <Skeleton className="h-6 w-20" />
                        </div>
                        <div className="flex gap-2 justify-end">
                          <Skeleton className="h-8 w-8" />
                          <Skeleton className="h-8 w-8" />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : questions.length === 0 ? (
              // Empty state
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">No questions found</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {filter.theme_id ? 'No questions found for the selected theme.' : 'No daily questions have been created yet.'}
                  </p>
                  <button
                    onClick={handleCreateQuestion}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create First Question
                  </button>
                </div>
              </div>
            ) : (
              // Questions grid
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 min-w-0 py-2">
                {questions.map((question) => {
                  // Find the theme for this question using theme_id
                  const questionTheme = themes.find(theme => theme.id === question.theme_id)

                  return (
                    <motion.div
                      key={question._id}
                      variants={cardVariants}
                      whileHover={{ y: -4, scale: 1.02 }}
                      transition={{ duration: 0.2 }}
                      className="bg-card border border-border rounded-xl p-5 hover:shadow-lg hover:border-primary/20 transition-all duration-200 group relative overflow-hidden min-w-0"
                    >
                      {/* Theme indicator stripe */}
                      {questionTheme && (
                        <div
                          className="absolute top-0 left-0 w-full h-1 rounded-t-xl"
                          style={{ backgroundColor: questionTheme.background_color }}
                        />
                      )}

                      <div className="space-y-4">
                        {/* Theme Badge */}
                        {questionTheme && (
                          <div className="flex items-center gap-2">
                            <span className="text-lg">{questionTheme.icon}</span>
                            <span
                              className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border"
                              style={{
                                backgroundColor: `${questionTheme.background_color}15`,
                                color: questionTheme.font_color,
                                borderColor: `${questionTheme.background_color}30`
                              }}
                            >
                              {questionTheme.name_en}
                            </span>
                          </div>
                        )}

                        {/* Question Text */}
                        <div className="space-y-3">
                          <div>
                            <p className="text-foreground font-semibold text-sm leading-relaxed line-clamp-2">
                              {question.text}
                            </p>
                          </div>

                          {question.text_en && question.text_en !== question.text && (
                            <div>
                              <p className="text-muted-foreground text-xs font-medium mb-1">English:</p>
                              <p className="text-muted-foreground text-xs leading-relaxed line-clamp-2">
                                {question.text_en}
                              </p>
                            </div>
                          )}
                        </div>

                        {/* Metadata */}
                        <div className="space-y-2">
                          {/* Smart Date Display - Updated if available, otherwise Created */}
                          {(question.updated_at || question.created_at) && (
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Clock className="w-3 h-3" />
                              <span>
                                {question.updated_at
                                  ? `Updated ${formatTimeAgo(question.updated_at)}`
                                  : `Created ${formatTimeAgo(question.created_at)}`
                                }
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2 justify-end pt-3 border-t border-border">
                          <button
                            onClick={() => handleEditQuestion(question)}
                            className="inline-flex items-center p-2 border border-border rounded-lg text-foreground bg-background hover:bg-accent transition-colors"
                            title="Edit question"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteQuestion(question._id || question.id)}
                            className="inline-flex items-center p-2 border border-border rounded-lg text-destructive bg-background hover:bg-destructive/10 transition-colors"
                            title="Delete question"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            )}
          </motion.div>
          </div>

          {/* Fixed Pagination at Bottom - Outside scrollable area */}
          {!loading && questions.length > 0 && (
            <motion.div
              variants={cardVariants}
              className="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-6 py-3"
            >
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Showing {((filter.page! - 1) * filter.limit!) + 1} to {Math.min(filter.page! * filter.limit!, totalItems)} of {totalItems} questions
                </div>

                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handlePageChange(filter.page! - 1)}
                    disabled={filter.page === 1}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>

                  <span className="text-sm text-gray-600 dark:text-gray-400 px-4">
                    Page {filter.page} of {totalPages}
                  </span>

                  <button
                    onClick={() => handlePageChange(filter.page! + 1)}
                    disabled={filter.page === totalPages}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </motion.div>
      </div>

      {/* Daily Question Modal */}
      <DailyQuestionModal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        question={editingQuestion}
        themes={themes}
        onSuccess={handleModalSuccess}
        loading={modalLoading}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteConfirm}
        onOpenChange={setShowDeleteConfirm}
        title="Delete Question"
        description="Are you sure you want to delete this question? This action cannot be undone and will permanently remove the question from the system."
        confirmText="Delete Question"
        cancelText="Cancel"
        onConfirm={confirmDeleteQuestion}
        variant="danger"
      />
    </MainLayout>
  )
}

export default DailyQuestions

